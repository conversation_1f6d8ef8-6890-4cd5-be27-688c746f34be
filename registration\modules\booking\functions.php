<?php
/**
 * Booking Functions
 *
 * This file contains functions for handling bookings.
 */

// Include required dependencies
// require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/constants.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/validation.php';

// Include API functions for external data fetching
require_once __DIR__ . '/../includes/api_functions.php';

/**
 * Get available slots for a date and poli
 *
 * @param  string $date   Date in Y-m-d format
 * @param  int    $poliId Poli ID
 * @return array Available slots
 */
function getAvailableSlots($date, $poliId)
{
    // Debug statements removed to prevent JSON parsing errors
    // print('<pre>'.print_r($date, true).'</pre>');
    // print('<pre>'.print_r($poliId, true).'</pre>');

    try {
        $db = getDbConnection();

        // Get day of week (0 = Sunday, 1 = Monday, etc.)
        $dayOfWeek = date('w', strtotime($date));

        // Get doctors and their schedules for the given poli and day
        $stmt = $db->prepare(
            "
            SELECT
                d.id AS doctor_id,
                d.name AS doctor_name,
                s.start_time,
                s.end_time,
                s.max_patients,
                (
                    SELECT COUNT(*)
                    FROM bookings b
                    WHERE b.doctor_id = d.id
                    AND b.booking_date = :date
                    AND b.status != 'cancelled'
                ) AS booked_count
            FROM doctors d
            JOIN schedules s ON d.id = s.doctor_id
            WHERE d.poli_id = :poli_id
            AND s.day_of_week = :day_of_week
            AND d.is_active = 1
            AND s.is_active = 1
        "
        );

        $stmt->bindParam(':poli_id', $poliId);
        $stmt->bindParam(':day_of_week', $dayOfWeek);
        $stmt->bindParam(':date', $date);
        $stmt->execute();

        $slots = [];

        while ($row = $stmt->fetch()) {
            // Calculate available slots
            $availableSlots = $row['max_patients'] - $row['booked_count'];

            if ($availableSlots > 0) {
                $slots[] = [
                    'doctor_id' => $row['doctor_id'],
                    'doctor_name' => $row['doctor_name'],
                    'start_time' => formatTime($row['start_time']),
                    'end_time' => formatTime($row['end_time']),
                    'available_slots' => $availableSlots
                ];
            }
        }

        return $slots;
    } catch (PDOException $e) {
        error_log("Error getting available slots: " . $e->getMessage());
        return [];
    }
}

/**
 * Check if patient has already booked the same doctor on the same date and time
 *
 * @param  int    $patientId Patient ID
 * @param  int    $doctorId  Doctor ID
 * @param  string $date      Date in Y-m-d format
 * @param  string $time      Time in H:i format
 * @return array  Array with 'exists' (bool) and 'booking_id' (int|null)
 */
function checkDuplicateBooking($patientId, $doctorId, $date, $time)
{
    try {
        $db = getDbConnection();

        $stmt = $db->prepare(
            "
            SELECT id
            FROM bookings
            WHERE patient_id = :patient_id
            AND doctor_id = :doctor_id
            AND booking_date = :booking_date
            AND booking_time = :booking_time
            AND status NOT IN ('cancelled')
            LIMIT 1
        "
        );

        $stmt->bindParam(':patient_id', $patientId);
        $stmt->bindParam(':doctor_id', $doctorId);
        $stmt->bindParam(':booking_date', $date);
        $stmt->bindParam(':booking_time', $time);
        $stmt->execute();

        $result = $stmt->fetch();

        if ($result) {
            return [
                'exists' => true,
                'booking_id' => $result['id']
            ];
        } else {
            return [
                'exists' => false,
                'booking_id' => null
            ];
        }
    } catch (PDOException $e) {
        error_log("Error checking duplicate booking: " . $e->getMessage());
        return [
            'exists' => false,
            'booking_id' => null
        ];
    }
}

/**
 * Create a booking
 *
 * @param  int    $patientId Patient ID
 * @param  int    $poliId    Poli ID
 * @param  int    $doctorId  Doctor ID
 * @param  string $date      Date in Y-m-d format
 * @param  string $time      Time in H:i format
 * @param  string $notes     Optional notes
 * @return array Result of booking creation
 */
function createBooking($patientId, $poliId, $doctorId, $date, $time, $notes = '')
{
    try {
        $db = getDbConnection();

        // Check if patient has already booked the same doctor on the same date and time
        $duplicateCheck = checkDuplicateBooking($patientId, $doctorId, $date, $time);
        if ($duplicateCheck['exists']) {
            return [
                'success' => false,
                'message' => ERROR_DUPLICATE_BOOKING,
                'data' => null,
                'duplicate_booking_id' => $duplicateCheck['booking_id']
            ];
        }

        // Note: Available slots check removed - will be implemented later with API

        // Get queue number for the day and doctor
        $queueNumber = getNextQueueNumber($doctorId, $date);

        // Generate booking number
        $bookingNumber = generateBookingNumber();

        // Create booking
        $stmt = $db->prepare(
            "
            INSERT INTO bookings (
                booking_number, patient_id, doctor_id, poli_id,
                booking_date, booking_time, queue_number, notes, status
            ) VALUES (
                :booking_number, :patient_id, :doctor_id, :poli_id,
                :booking_date, :booking_time, :queue_number, :notes, :status
            )
        "
        );

        $status = BOOKING_CONFIRMED;

        $stmt->bindParam(':booking_number', $bookingNumber);
        $stmt->bindParam(':patient_id', $patientId);
        $stmt->bindParam(':doctor_id', $doctorId);
        $stmt->bindParam(':poli_id', $poliId);
        $stmt->bindParam(':booking_date', $date);
        $stmt->bindParam(':booking_time', $time);
        $stmt->bindParam(':queue_number', $queueNumber);
        $stmt->bindParam(':notes', $notes);
        $stmt->bindParam(':status', $status);

        $stmt->execute();

        // Get the newly created booking
        $bookingId = $db->lastInsertId();
        $booking = getBookingDetails($bookingId);

        return [
            'success' => true,
            'message' => SUCCESS_BOOKING,
            'data' => $booking
        ];
    } catch (PDOException $e) {
        error_log("Error creating booking: " . $e->getMessage());
        return [
            'success' => false,
            'message' => ERROR_BOOKING_FAILED,
            'data' => null
        ];
    }
}

/**
 * Cancel a booking
 *
 * @param  int $bookingId Booking ID
 * @return array Result of cancellation
 */
function cancelBooking($bookingId)
{
    try {
        $db = getDbConnection();

        $stmt = $db->prepare(
            "
            UPDATE bookings SET
                status = :status,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = :id
        "
        );

        $status = BOOKING_CANCELLED;

        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':id', $bookingId);

        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Booking cancelled successfully',
            'data' => null
        ];
    } catch (PDOException $e) {
        error_log("Error cancelling booking: " . $e->getMessage());
        return [
            'success' => false,
            'message' => ERROR_SYSTEM,
            'data' => null
        ];
    }
}

/**
 * Get booking details
 *
 * @param  int $bookingId Booking ID
 * @return array|null Booking details if exists, null otherwise
 */
function getBookingDetails($bookingId)
{
    try {
        $db = getDbConnection();

        $stmt = $db->prepare(
            "
            SELECT
                b.*,
                p.name AS patient_name,
                p.medical_record_number,
                d.name AS doctor_name,
                po.name AS poli_name
            FROM bookings b
            JOIN patients p ON b.patient_id = p.id
            JOIN doctors d ON b.doctor_id = d.id
            JOIN poli po ON b.poli_id = po.id
            WHERE b.id = :id
            LIMIT 1
        "
        );

        $stmt->bindParam(':id', $bookingId);
        $stmt->execute();

        $booking = $stmt->fetch();

        return $booking ?: null;
    } catch (PDOException $e) {
        error_log("Error getting booking details: " . $e->getMessage());
        return null;
    }
}

/**
 * Get next queue number for a doctor and date
 *
 * @param  int    $doctorId Doctor ID
 * @param  string $date     Date in Y-m-d format
 * @return int Next queue number
 */
function getNextQueueNumber($doctorId, $date)
{
    try {
        $db = getDbConnection();

        $stmt = $db->prepare(
            "
            SELECT MAX(queue_number) AS max_queue
            FROM bookings
            WHERE doctor_id = :doctor_id
            AND booking_date = :date
        "
        );

        $stmt->bindParam(':doctor_id', $doctorId);
        $stmt->bindParam(':date', $date);
        $stmt->execute();

        $result = $stmt->fetch();

        return ($result['max_queue'] ?? 0) + 1;
    } catch (PDOException $e) {
        error_log("Error getting next queue number: " . $e->getMessage());
        return 1;
    }
}

/**
 * Normalize API registration data to match local database structure
 *
 * @param  array $apiData Raw API response data
 * @return array Normalized booking data
 */
function normalizeApiRegistrationData($apiData)
{
    $normalizedBookings = [];

    // Check if API data has the expected structure
    if (!isset($apiData['data']) || !is_array($apiData['data'])) {
        return $normalizedBookings;
    }

    // Handle different possible API response structures
    $registrations = [];
    if (isset($apiData['data']['_resources']) && is_array($apiData['data']['_resources'])) {
        $registrations = $apiData['data']['_resources'];
    } elseif (isset($apiData['data']['data']['_resources']) && is_array($apiData['data']['data']['_resources'])) {
        $registrations = $apiData['data']['data']['_resources'];
    } elseif (is_array($apiData['data'])) {
        $registrations = $apiData['data'];
    }

    foreach ($registrations as $registration) {
        // Map API fields to local database field names
        $normalizedBooking = [
            'id' => $registration['id'] ?? null,
            'booking_number' => $registration['registration_number'] ?? $registration['booking_number'] ?? '',
            'patient_name' => $registration['patient']['name'] ?? $registration['patient_name'] ?? '',
            'nik' => $registration['patient']['nik'] ?? $registration['nik'] ?? '',
            'medical_record_number' => $registration['patient']['medical_record_number'] ?? '',
            'doctor_name' => $registration['doctor']['name'] ?? $registration['doctor_name'] ?? '',
            'poli_name' => $registration['clinic']['name'] ?? $registration['poli_name'] ?? '',
            'booking_date' => $registration['visit_date'] ?? $registration['booking_date'] ?? '',
            'booking_time' => $registration['visit_time'] ?? $registration['booking_time'] ?? '',
            'queue_number' => $registration['queue_number'] ?? '',
            'status' => $registration['status'] ?? 'confirmed',
            'notes' => $registration['notes'] ?? '',
            'created_at' => $registration['created_at'] ?? '',
            'updated_at' => $registration['updated_at'] ?? ''
        ];

        $normalizedBookings[] = $normalizedBooking;
    }

    return $normalizedBookings;
}

/**
 * Get booking by NIK using API only
 *
 * @param  string $nik Patient's NIK
 * @return array|null Booking details if exists, null otherwise
 */
function getBookingByNIK($nik)
{
    // Validate NIK parameter
    if (empty($nik)) {
        error_log("getBookingByNIK: Empty NIK provided");
        return null;
    }

    // Trim and validate NIK
    $nik = trim($nik);
    if (strlen($nik) < 10) {
        error_log("getBookingByNIK: Invalid NIK format - " . $nik);
        return null;
    }

    // Fetch patient registrations from API
    $apiResult = fetchPatientRegistrationsByNIK($nik);

    // Log the API result for debugging
    error_log("getBookingByNIK API result for NIK {$nik}: " . print_r($apiResult, true));

    // Check if API call was successful
    if (!$apiResult['success']) {
        error_log("getBookingByNIK: API call failed - " . ($apiResult['error'] ?? 'Unknown error'));
        return null;
    }

    // Check if we have data
    if (empty($apiResult['data'])) {
        error_log("getBookingByNIK: No data returned from API for NIK {$nik}");
        return null;
    }

    // Normalize the API data using existing function
    $normalizedBookings = normalizeApiRegistrationData($apiResult);

    // Check if we have any bookings after normalization
    if (empty($normalizedBookings)) {
        error_log("getBookingByNIK: No bookings found after normalization for NIK {$nik}");
        return null;
    }

    // Determine if we have multiple bookings
    $isMultiple = count($normalizedBookings) > 1;

    // Return the result in a consistent format
    $result = [
        'is_multiple' => $isMultiple,
        'bookings' => $normalizedBookings,
        'count' => count($normalizedBookings)
    ];

    error_log("getBookingByNIK: Found " . count($normalizedBookings) . " booking(s) for NIK {$nik}");

    return $result;
}

/**
 * Get single booking by NIK and booking number (for ticket display)
 *
 * @param  string $nik Patient's NIK
 * @param  string $bookingNumber Optional booking number to get specific booking
 * @return array|null Single booking details if exists, null otherwise
 */
function getSingleBookingByNIK($nik, $bookingNumber = null)
{
    // Get all bookings for the NIK
    $bookingsResult = getBookingByNIK($nik);

    if (!$bookingsResult || empty($bookingsResult['bookings'])) {
        return null;
    }

    $bookings = $bookingsResult['bookings'];

    // If booking number is specified, find that specific booking
    if (!empty($bookingNumber)) {
        foreach ($bookings as $booking) {
            if ($booking['booking_number'] === $bookingNumber) {
                return $booking;
            }
        }
        return null; // Booking number not found
    }

    // If no booking number specified, return the most recent booking
    // Assuming bookings are ordered by date, get the first one
    return $bookings[0];
}

/**
 * Process booking form
 *
 * @return array Result of processing
 */
function processBookingForm()
{
    // Check if form is submitted
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        return [
            'success' => false,
            'message' => 'Form not submitted',
            'data' => null
        ];
    }

    // Get form data
    $data = [
        'poli_id' => isset($_POST['poli_id']) ? (int)$_POST['poli_id'] : 0,
        'doctor_id' => isset($_POST['doctor_id']) ? (int)$_POST['doctor_id'] : 0,
        'booking_date' => isset($_POST['booking_date']) ? trim($_POST['booking_date']) : '',
        'booking_time' => isset($_POST['booking_time']) ? trim($_POST['booking_time']) : '',
        'notes' => isset($_POST['notes']) ? trim($_POST['notes']) : ''
    ];

    // Validate required fields
    $requiredFields = ['poli_id', 'doctor_id', 'booking_date', 'booking_time'];
    $missingFields = validateRequiredFields($data, $requiredFields);

    if (!empty($missingFields)) {
        return [
            'success' => false,
            'message' => ERROR_REQUIRED_FIELDS,
            'data' => null,
            'missing_fields' => $missingFields
        ];
    }

    // Validate date
    if (!validateDate($data['booking_date'])) {
        return [
            'success' => false,
            'message' => 'Tanggal tidak valid',
            'data' => null
        ];
    }

    // Validate time
    if (!validateTime($data['booking_time'])) {
        return [
            'success' => false,
            'message' => 'Waktu tidak valid',
            'data' => null
        ];
    }

    // Create booking
    $patientId = $_SESSION['patient_data']['id'];
    $result = createBooking(
        $patientId,
        $data['poli_id'],
        $data['doctor_id'],
        $data['booking_date'],
        $data['booking_time'],
        $data['notes']
    );

    // If successful, store booking data in session and move to ticket step
    if ($result['success']) {
        $_SESSION['booking_data'] = $result['data'];
        setCurrentStep(STEP_TICKET);
    }

    return $result;
}
