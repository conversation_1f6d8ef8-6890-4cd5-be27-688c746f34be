<?php
/**
 * Example: How to Use the Improved Ticket Template
 * 
 * This file shows different ways to use the ticket template with booking data
 */

// Start session
session_start();

// Include necessary files
require_once __DIR__ . '/modules/booking/functions.php';

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket Template Usage Examples</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <h2>Ticket Template Usage Examples</h2>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-code"></i> Method 1: Direct Booking Data</h5>
                </div>
                <div class="card-body">
                    <p>Pass booking data directly to the template:</p>
                    <pre><code>&lt;?php
// Set booking data
$bookingData = [
    'booking_number' => 'BK20241201001',
    'queue_number' => '5',
    'patient_name' => 'John Doe',
    'nik' => '1234567890123456',
    'poli_name' => 'Poli Umum',
    'doctor_name' => 'Dr. Jane Smith',
    'booking_date' => '2024-12-01',
    'booking_time' => '10:00',
    'status' => 'confirmed'
];

// Include template
include 'templates/ticket_template.php';
?&gt;</code></pre>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-database"></i> Method 2: From API by NIK</h5>
                </div>
                <div class="card-body">
                    <p>Fetch booking data from API using NIK:</p>
                    <pre><code>&lt;?php
// Get NIK from form or URL
$nik = $_GET['nik'] ?? '3302194812050002';

// Template will automatically fetch data
// Just include it with NIK in URL or GET parameter
include 'templates/ticket_template.php';
?&gt;</code></pre>
                    <p><strong>URL Example:</strong><br>
                    <code>ticket.php?nik=3302194812050002</code></p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-session"></i> Method 3: Session Data</h5>
                </div>
                <div class="card-body">
                    <p>Store booking data in session:</p>
                    <pre><code>&lt;?php
// Store in session (usually after booking creation)
$_SESSION['booking_data'] = [
    'booking_number' => 'BK20241201001',
    'queue_number' => '5',
    'patient_name' => 'John Doe',
    // ... other booking data
];

// Template will automatically use session data
include 'templates/ticket_template.php';
?&gt;</code></pre>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-link"></i> Method 4: Specific Booking</h5>
                </div>
                <div class="card-body">
                    <p>Get specific booking by NIK and booking number:</p>
                    <pre><code>&lt;?php
// URL with both NIK and booking number
// ticket.php?nik=3302194812050002&booking=BK20241201001

// Template will fetch the specific booking
include 'templates/ticket_template.php';
?&gt;</code></pre>
                    <p><strong>URL Example:</strong><br>
                    <code>ticket.php?nik=3302194812050002&booking=BK20241201001</code></p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="alert alert-info mt-4">
        <h5><i class="fas fa-info-circle"></i> Template Features</h5>
        <div class="row">
            <div class="col-md-6">
                <ul>
                    <li><strong>Automatic Data Fetching:</strong> Fetches booking data from API using improved functions</li>
                    <li><strong>Multiple Data Sources:</strong> Supports direct data, session, URL parameters</li>
                    <li><strong>Error Handling:</strong> Shows appropriate message when no data found</li>
                    <li><strong>QR Code Generation:</strong> Creates QR code for booking verification</li>
                </ul>
            </div>
            <div class="col-md-6">
                <ul>
                    <li><strong>Indonesian Date Format:</strong> Formats dates as "DD MMM, YYYY"</li>
                    <li><strong>Status Indicator:</strong> Shows booking status with colored badge</li>
                    <li><strong>PDF Download:</strong> Generate and download ticket as PDF</li>
                    <li><strong>Responsive Design:</strong> Works on mobile and desktop</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="alert alert-success mt-4">
        <h5><i class="fas fa-check-circle"></i> QR Code Features</h5>
        <p>The QR code generated contains a verification URL that includes:</p>
        <ul>
            <li><strong>Patient NIK:</strong> For patient identification</li>
            <li><strong>Booking Number:</strong> For specific booking verification</li>
            <li><strong>Verification URL:</strong> Points to caribooking.php for booking lookup</li>
        </ul>
        <p><strong>QR Code URL Format:</strong><br>
        <code>http://periksa-dokter.local/registration/caribooking.php?nik=NIK&booking=BOOKING_NUMBER</code></p>
    </div>
    
    <div class="text-center mt-4">
        <a href="test_improved_ticket.php" class="btn btn-primary">
            <i class="fas fa-test-tube"></i> Test the Improved Ticket Template
        </a>
        <a href="templates/ticket_template.php?nik=3302194812050002" class="btn btn-secondary">
            <i class="fas fa-ticket-alt"></i> View Template Directly
        </a>
    </div>
</div>

</body>
</html>
