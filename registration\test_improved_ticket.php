<?php
/**
 * Test Improved Ticket Template
 * 
 * This file demonstrates how to use the improved ticket template with booking data from API
 */

// Start session
session_start();

// Include necessary files
require_once __DIR__ . '/modules/booking/functions.php';

// Test NIK (you can change this to test with different NIKs)
$testNik = '3302194812050002'; // Replace with a valid NIK that has booking data

// Get booking data using the improved function
$bookingsResult = getBookingByNIK($testNik);

echo "<!DOCTYPE html>";
echo "<html lang='id'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Test Improved Ticket Template</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";

echo "<div class='container mt-4'>";
echo "<h2>Test Improved Ticket Template</h2>";

// Debug information
echo "<div class='alert alert-info'>";
echo "<h5><i class='fas fa-info-circle'></i> Debug Information</h5>";
echo "<p><strong>Test NIK:</strong> " . htmlspecialchars($testNik) . "</p>";

if ($bookingsResult) {
    echo "<p><strong>Bookings Found:</strong> " . $bookingsResult['count'] . "</p>";
    echo "<p><strong>Multiple Bookings:</strong> " . ($bookingsResult['is_multiple'] ? 'Yes' : 'No') . "</p>";
    
    echo "<h6>Booking Data:</h6>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px;'>";
    print_r($bookingsResult['bookings']);
    echo "</pre>";
} else {
    echo "<p><strong>Result:</strong> No bookings found for this NIK</p>";
}
echo "</div>";

// Test the ticket template
if ($bookingsResult && !empty($bookingsResult['bookings'])) {
    // Use the first booking for the ticket
    $bookingData = $bookingsResult['bookings'][0];
    
    echo "<div class='alert alert-success'>";
    echo "<h5><i class='fas fa-check-circle'></i> Ticket Template Test</h5>";
    echo "<p>Using the first booking found to generate the ticket:</p>";
    echo "</div>";
    
    // Include the ticket template
    include 'templates/ticket_template.php';
    
} else {
    echo "<div class='alert alert-warning'>";
    echo "<h5><i class='fas fa-exclamation-triangle'></i> No Booking Data</h5>";
    echo "<p>No booking data found for NIK: " . htmlspecialchars($testNik) . "</p>";
    echo "<p>To test the ticket template:</p>";
    echo "<ol>";
    echo "<li>Make sure you have booking data in the API for the test NIK</li>";
    echo "<li>Or change the <code>\$testNik</code> variable in this file to a NIK that has bookings</li>";
    echo "<li>Or test the template with URL parameters: <code>?nik=YOUR_NIK&booking=BOOKING_NUMBER</code></li>";
    echo "</ol>";
    echo "</div>";
    
    // Show the template anyway to demonstrate the "no data" case
    echo "<h5>Template with No Data (demonstrates error handling):</h5>";
    include 'templates/ticket_template.php';
}

echo "<hr>";
echo "<div class='alert alert-secondary'>";
echo "<h5><i class='fas fa-lightbulb'></i> How to Use the Improved Ticket Template</h5>";
echo "<ol>";
echo "<li><strong>Direct booking data:</strong> Pass <code>\$bookingData</code> variable before including the template</li>";
echo "<li><strong>Session data:</strong> Store booking data in <code>\$_SESSION['booking_data']</code></li>";
echo "<li><strong>URL parameters:</strong> Use <code>?nik=NIK&booking=BOOKING_NUMBER</code> in the URL</li>";
echo "<li><strong>Session NIK:</strong> Store patient NIK in <code>\$_SESSION['patient_data']['nik']</code></li>";
echo "</ol>";
echo "<p><strong>Features:</strong></p>";
echo "<ul>";
echo "<li>Automatically fetches booking data from API using improved <code>getBookingByNIK()</code> function</li>";
echo "<li>Handles multiple data sources with priority order</li>";
echo "<li>Shows appropriate message when no booking data is found</li>";
echo "<li>Generates QR code with verification URL</li>";
echo "<li>Formats dates in Indonesian format</li>";
echo "<li>Includes booking status indicator</li>";
echo "<li>PDF download functionality</li>";
echo "</ul>";
echo "</div>";

echo "</div>"; // container

echo "</body>";
echo "</html>";
?>
