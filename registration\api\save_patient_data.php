<?php
/**
 * Save Patient Data API
 *
 * This API saves patient data and returns the result.
 */

// Prevent any output before JSON response
// Start session for compatibility
session_start();

// Include configuration files 
require_once '../config/constants.php';

// Include utility functions
require_once '../includes/functions.php';
require_once '../includes/validation.php';
require_once '../includes/cookie_functions.php';
require_once '../includes/api_functions.php'; 

// Set content type to JSON
header('Content-Type: application/json');

// Clear any output that might have been generated
ob_clean();

// Set content type to JSON
header('Content-Type: application/json');

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(
        [
        'success' => false,
        'message' => 'Invalid request method'
        ]
    );
    exit;
}

// Get NIK from request first, then check session as fallback
$nik = isset($_POST['nik']) ? trim($_POST['nik']) : '';

// If NIK is not provided in POST, check session as fallback
if (empty($nik) && isset($_SESSION['nik'])) {
    $nik = $_SESSION['nik'];
}

// Debug logging
error_log("save_patient_data.php - NIK from POST: " . (isset($_POST['nik']) ? $_POST['nik'] : 'not set'));
error_log("save_patient_data.php - NIK from session: " . (isset($_SESSION['nik']) ? $_SESSION['nik'] : 'not set'));
error_log("save_patient_data.php - Final NIK: " . $nik);

// If still no NIK, return error
if (empty($nik)) {
    echo json_encode(
        [
        'success' => false,
        'message' => 'NIK not found in session or request data'
        ]
    );
    exit;
}

// Sanitize all POST data
$sanitizedPost = sanitizeInputArray($_POST);

// Get patient data from request with sanitized values
$formData = [
    'nik' => $nik,
    'name' => isset($sanitizedPost['name']) ? $sanitizedPost['name'] : '',
    'birth_date' => isset($sanitizedPost['birth_date']) ? $sanitizedPost['birth_date'] : '',
    'birth_place' => isset($sanitizedPost['birth_place']) ? $sanitizedPost['birth_place'] : '',
    'gender' => isset($sanitizedPost['gender']) ? $sanitizedPost['gender'] : '',
    'education_id' => isset($sanitizedPost['education_id']) ? $sanitizedPost['education_id'] : null,
    'profession_id' => isset($sanitizedPost['profession_id']) ? $sanitizedPost['profession_id'] : null,
    'marital_id' => isset($sanitizedPost['marital_id']) ? $sanitizedPost['marital_id'] : null,
    'religion_id' => isset($sanitizedPost['religion_id']) ? $sanitizedPost['religion_id'] : null,
    'address' => isset($sanitizedPost['address']) ? $sanitizedPost['address'] : '',
    'village_id' => isset($sanitizedPost['village_id']) ? $sanitizedPost['village_id'] : null,
    'phone' => isset($sanitizedPost['phone']) ? $sanitizedPost['phone'] : '',
    'email' => isset($sanitizedPost['email']) ? $sanitizedPost['email'] : '',
    'job' => isset($sanitizedPost['job']) ? $sanitizedPost['job'] : '',
    'current_address' => isset($sanitizedPost['current_address']) ? $sanitizedPost['current_address'] : '',
    'is_same_address' => isset($sanitizedPost['is_same_address']) ? (bool)$sanitizedPost['is_same_address'] : false,
    'passport' => isset($sanitizedPost['passport']) ? $sanitizedPost['passport'] : '',
    'kitas' => isset($sanitizedPost['kitas']) ? $sanitizedPost['kitas'] : '',
    'is_active' => isset($sanitizedPost['is_active']) ? (bool)$sanitizedPost['is_active'] : true,
    'institution' => isset($sanitizedPost['institution']) ? $sanitizedPost['institution'] : '',
    'current_village_id' => isset($sanitizedPost['current_village_id']) ? $sanitizedPost['current_village_id'] : null,
    'person_in_charge' => isset($sanitizedPost['person_in_charge']) ? $sanitizedPost['person_in_charge'] : ''
];

// Validate required fields
$requiredFields = ['name', 'birth_date', 'birth_place', 'gender', 'address', 'phone'];
$missingFields = validateRequiredFields($formData, $requiredFields);

if (!empty($missingFields)) {
    echo json_encode(
        [
        'success' => false,
        'message' => 'Missing required fields',
        'missing_fields' => $missingFields
        ]
    );
    exit;
}

// Validate email if provided
if (!empty($formData['email']) && !validateEmail($formData['email'])) {
    echo json_encode(
        [
        'success' => false,
        'message' => 'Invalid email format'
        ]
    );
    exit;
}

// Validate phone
if (!validatePhone($formData['phone'])) {
    echo json_encode(
        [
        'success' => false,
        'message' => 'Invalid phone number format'
        ]
    );
    exit;
}

// Validate birth date
if (!validateDate($formData['birth_date'])) {
    echo json_encode(
        [
        'success' => false,
        'message' => 'Invalid birth date format (should be YYYY-MM-DD)'
        ]
    );
    exit;
}

// Format data for SimKlinik API
$apiData = [
    'name' => $formData['name'],
    'nik' => $formData['nik'],
    'date_of_birth' => $formData['birth_date'],
    'place_of_birth' => $formData['birth_place'],
    'phone' => $formData['phone'],
    'job' => $formData['job'],
    'religion' => [
        'id' => (int)$formData['religion_id']
    ],
    'address' => $formData['address'],
    'current_address' => $formData['current_address'] ?: $formData['address'],
    'is_same_address' => $formData['is_same_address'],
    'person_in_charge' => $formData['person_in_charge'],
    'gender' => [
        'id' => (int)$formData['gender']
    ],
    'village' => [
        'id' => (int)$formData['village_id']
    ],
    'current_village' => [
        // Use current_village_id if it exists, otherwise use village_id
        'id' => (int)(!empty($formData['current_village_id']) ?
            $formData['current_village_id'] :
            $formData['village_id'])
    ],
    'marital' => [
        'id' => (int)$formData['marital_id']
    ],
    'education' => [
        'id' => (int)$formData['education_id']
    ],
    'profession' => [
        'id' => (int)$formData['profession_id']
    ],
    'passport' => $formData['passport'],
    'kitas' => $formData['kitas'],
    'is_active' => $formData['is_active'],
    'institution' => $formData['institution']
];

// Send data to SimKlinik API
$apiResult = createPerson($apiData); 
// Debug logging to see what's happening
error_log("save_patient_data.php - API Result: " . print_r($apiResult, true));

// Determine the final result based on API response only
if ($apiResult['success']) {
    $result = [
        'success' => true,
        'message' => 'Patient data saved successfully',
        'data' => $apiResult['data']
    ];

    // Store patient data in session
    $_SESSION['patient_data'] = $apiResult['data'];
} else {
    $result = [
        'success' => false,
        'message' => 'Failed to save patient data: ' . ($apiResult['error'] ?? 'Unknown error'),
        'data' => null,
        'api_error' => $apiResult['error']
    ];
}

// Return result
echo json_encode($result);

// End output buffering and flush
ob_end_flush();
